import Footer from "../components/Footer/Footer";
import HomeCard from "../components/ui/Cards/HomeCard";
import HomeText from "../components/ui/Text/HomeText";
import HomeTitle from "../components/ui/Titels/HomeTitle";
import { homeCard, homeTitleText } from "../data/homeData";

export default function Home() {
  return (
    <>
    <section className="bg-black  relative h-screen home ">
      <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-black/70 via-[#0A1A5A]/50 to-black/70"></div>
      <div className="relative z-10 h-full text-white flex-col flex justify-center items-center ">
        
        <img src="/src/assets/Logo/home-logo.svg" className=" 2xl:w-[200px]" alt="" />
        {/* title */}
        <HomeTitle text={homeTitleText.text}/>
        <HomeText text={homeTitleText.secondeText}/>
        <div className=" mt-10 flex justify-between items-center w-[550px]">
            {homeCard.map((e,i)=> (
                <HomeCard key={i} icon={e.icon} text={e.text}/>
            ))}
        </div>
        <div className=" mt-10">
            <img src="/src/assets/homeImage/Vector (4).png" alt="" />
        </div>
        <div className=" mt-10 justify-between items-center flex w-[550px]">
            <button className="py-5 min-w-[250px]  px-12 rounded-full text-xl font-bold bg-black text-white">join as a Brand</button>
            <button className="py-5 min-w-[250px] flex gap-2.5 items-center justify-center px-12  rounded-full text-xl font-bold text-black bg-[#D4AF37]">Enter the Mall
              <img src="/src/assets/homeImage/Vector (5).svg" className="h-5 w-5" alt="" />
            </button>
        </div>
      </div>

    </section>
    <Footer/>
    </>

  )
}
