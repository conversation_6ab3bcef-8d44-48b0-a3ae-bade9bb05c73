import Store from './Store'

// Sample store data - you can move this to a separate data file later
const storesData = [
  {
    id: 1,
    name: "<PERSON>",
    position: [-20, 0, -30],
    color: "#FF6B35",
    website: "https://www.nike.com",
    logo: "/assets/brands/nike-logo.png"
  },
  {
    id: 2,
    name: "<PERSON>",
    position: [0, 0, -30],
    color: "#007AFF",
    website: "https://www.apple.com",
    logo: "/assets/brands/apple-logo.png"
  },
  {
    id: 3,
    name: "Adidas",
    position: [20, 0, -30],
    color: "#000000",
    website: "https://www.adidas.com",
    logo: "/assets/brands/adidas-logo.png"
  },
  {
    id: 4,
    name: "<PERSON>",
    position: [-30, 0, 0],
    color: "#1428A0",
    website: "https://www.samsung.com",
    logo: "/assets/brands/samsung-logo.png"
  },
  {
    id: 5,
    name: "<PERSON><PERSON>",
    position: [30, 0, 0],
    color: "#000000",
    website: "https://www.zara.com",
    logo: "/assets/brands/zara-logo.png"
  },
  {
    id: 6,
    name: "H&M",
    position: [-20, 0, 30],
    color: "#E50000",
    website: "https://www2.hm.com",
    logo: "/assets/brands/hm-logo.png"
  },
  {
    id: 7,
    name: "Starbucks",
    position: [0, 0, 30],
    color: "#00704A",
    website: "https://www.starbucks.com",
    logo: "/assets/brands/starbucks-logo.png"
  },
  {
    id: 8,
    name: "McDonald's",
    position: [20, 0, 30],
    color: "#FFC72C",
    website: "https://www.mcdonalds.com",
    logo: "/assets/brands/mcdonalds-logo.png"
  }
]

export default function StoreManager() {
  return (
    <group>
      {storesData.map((store) => (
        <Store
          key={store.id}
          id={store.id}
          name={store.name}
          position={store.position}
          color={store.color}
          website={store.website}
          logo={store.logo}
        />
      ))}
    </group>
  )
}

export { storesData }
