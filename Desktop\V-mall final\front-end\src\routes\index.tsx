import { lazy, Suspense, type JSX } from "react";
import { createBrowserRouter } from "react-router-dom";
const Home = lazy(() => import("../pages/Home"));

const withSuspense = (Component: React.LazyExoticComponent<() => JSX.Element>) => (
    <Suspense fallback={<div className="text-center">جاري التحميل...</div>}>
      <Component />
    </Suspense>
  );
/* جميع المسارات الاساسية و الفرعية في هذا الملف تكون  */
const router = createBrowserRouter([
    {
        path : '/' ,
        element : withSuspense(Home),
    },
]);

export default router;