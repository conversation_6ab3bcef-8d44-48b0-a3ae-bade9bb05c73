import { useRef, useState } from 'react'
import { useFrame, useThree } from '@react-three/fiber'
import { Capsule, useKeyboardControls } from '@react-three/drei'
import { Vector3, Mesh } from 'three'

// Define keyboard controls
export const Controls = {
  forward: 'forward',
  backward: 'backward',
  left: 'left',
  right: 'right',
  jump: 'jump',
}

export const keyboardMap = [
  { name: Controls.forward, keys: ['ArrowUp', 'KeyW'] },
  { name: Controls.backward, keys: ['ArrowDown', 'KeyS'] },
  { name: Controls.left, keys: ['ArrowLeft', 'KeyA'] },
  { name: Controls.right, keys: ['ArrowRight', 'KeyD'] },
  { name: Controls.jump, keys: ['Space'] },
]

export default function Avatar() {
  const avatarRef = useRef<Mesh>(null)
  const [position, setPosition] = useState(new Vector3(0, 2, 15))
  const [velocity, setVelocity] = useState(new Vector3(0, 0, 0))
  const { camera } = useThree()
  
  const [, get] = useKeyboardControls()

  // Movement constants
  const SPEED = 5
  const JUMP_FORCE = 8
  const GRAVITY = -20
  const GROUND_Y = 1

  useFrame((state, delta) => {
    if (!avatarRef.current) return

    const { forward, backward, left, right, jump } = get()
    
    // Get camera direction for movement
    const cameraDirection = new Vector3()
    camera.getWorldDirection(cameraDirection)
    cameraDirection.y = 0
    cameraDirection.normalize()
    
    const cameraRight = new Vector3()
    cameraRight.crossVectors(cameraDirection, new Vector3(0, 1, 0))
    
    // Calculate movement
    const moveDirection = new Vector3(0, 0, 0)
    
    if (forward) moveDirection.add(cameraDirection)
    if (backward) moveDirection.sub(cameraDirection)
    if (left) moveDirection.sub(cameraRight)
    if (right) moveDirection.add(cameraRight)
    
    moveDirection.normalize()
    moveDirection.multiplyScalar(SPEED * delta)
    
    // Apply movement
    const newPosition = position.clone()
    newPosition.x += moveDirection.x
    newPosition.z += moveDirection.z
    
    // Apply gravity and jumping
    let newVelocity = velocity.clone()
    
    if (jump && position.y <= GROUND_Y + 0.1) {
      newVelocity.y = JUMP_FORCE
    }
    
    newVelocity.y += GRAVITY * delta
    newPosition.y += newVelocity.y * delta
    
    // Ground collision
    if (newPosition.y < GROUND_Y) {
      newPosition.y = GROUND_Y
      newVelocity.y = 0
    }
    
    // Boundary constraints (keep avatar within mall)
    newPosition.x = Math.max(-45, Math.min(45, newPosition.x))
    newPosition.z = Math.max(-45, Math.min(45, newPosition.z))
    
    setPosition(newPosition)
    setVelocity(newVelocity)
    
    // Update avatar position
    avatarRef.current.position.copy(newPosition)
    
    // Update camera to follow avatar
    const cameraOffset = new Vector3(0, 5, 10)
    const idealCameraPosition = newPosition.clone().add(cameraOffset)
    camera.position.lerp(idealCameraPosition, 0.1)
    camera.lookAt(newPosition)
  })

  return (
    <group>
      {/* Avatar Body */}
      <Capsule 
        ref={avatarRef}
        args={[0.5, 1]} 
        position={[position.x, position.y, position.z]}
        castShadow
      >
        <meshStandardMaterial 
          color="#4a90e2" 
          roughness={0.3}
          metalness={0.1}
        />
      </Capsule>
      
      {/* Avatar Head */}
      <mesh position={[position.x, position.y + 1.2, position.z]} castShadow>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial 
          color="#ffdbac" 
          roughness={0.8}
        />
      </mesh>
      
      {/* Avatar Eyes */}
      <mesh position={[position.x - 0.1, position.y + 1.3, position.z + 0.25]} castShadow>
        <sphereGeometry args={[0.05, 8, 8]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      
      <mesh position={[position.x + 0.1, position.y + 1.3, position.z + 0.25]} castShadow>
        <sphereGeometry args={[0.05, 8, 8]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
    </group>
  )
}
