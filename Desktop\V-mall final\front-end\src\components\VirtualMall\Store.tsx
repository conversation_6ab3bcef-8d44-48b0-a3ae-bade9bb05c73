import { useRef, useState } from 'react'
import { useFrame } from '@react-three/fiber'
import { Box, Text, Plane } from '@react-three/drei'
import { Mesh, Vector3 } from 'three'
import { useStoreInteraction } from '../../hooks/useStoreInteraction'

interface StoreProps {
  id: number
  name: string
  position: [number, number, number]
  color: string
  website: string
  logo: string
}

export default function Store({ id, name, position, color, website, logo }: StoreProps) {
  const storeRef = useRef<Mesh>(null)
  const [hovered, setHovered] = useState(false)
  const { isNearStore, showShopButton } = useStoreInteraction(id, new Vector3(...position))

  useFrame(() => {
    if (storeRef.current) {
      // Add subtle animation when hovered
      const scale = hovered ? 1.05 : 1
      storeRef.current.scale.setScalar(scale)
    }
  })

  const handleStoreClick = () => {
    window.open(website, '_blank')
  }

  return (
    <group position={position}>
      {/* Store Building */}
      <Box 
        ref={storeRef}
        args={[8, 8, 4]} 
        position={[0, 4, 0]}
        castShadow
        receiveShadow
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
        onClick={handleStoreClick}
      >
        <meshStandardMaterial 
          color={hovered ? '#ffffff' : color} 
          roughness={0.3}
          metalness={0.1}
          transparent
          opacity={0.9}
        />
      </Box>

      {/* Store Front Glass */}
      <Plane 
        args={[7, 6]} 
        position={[0, 3, 2.1]}
        receiveShadow
      >
        <meshStandardMaterial 
          color="#87CEEB" 
          transparent 
          opacity={0.3}
          roughness={0.1}
          metalness={0.9}
        />
      </Plane>

      {/* Store Sign */}
      <Box 
        args={[6, 1.5, 0.2]} 
        position={[0, 7.5, 2.2]}
        castShadow
      >
        <meshStandardMaterial 
          color={color} 
          roughness={0.2}
          metalness={0.3}
        />
      </Box>

      {/* Store Name Text */}
      <Text
        position={[0, 7.5, 2.3]}
        fontSize={0.8}
        color="white"
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.woff"
      >
        {name}
      </Text>

      {/* Store Logo Area */}
      <Plane 
        args={[2, 2]} 
        position={[0, 5, 2.15]}
      >
        <meshStandardMaterial 
          color="white" 
          roughness={0.1}
        />
      </Plane>

      {/* Logo Text (placeholder - you can replace with actual images later) */}
      <Text
        position={[0, 5, 2.16]}
        fontSize={0.3}
        color={color}
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.woff"
      >
        {name[0]}
      </Text>

      {/* Store Entrance */}
      <Box 
        args={[3, 6, 0.1]} 
        position={[0, 3, 2.05]}
      >
        <meshStandardMaterial 
          color="#8B4513" 
          roughness={0.8}
        />
      </Box>

      {/* Door Handles */}
      <Box 
        args={[0.1, 0.3, 0.1]} 
        position={[-0.8, 3, 2.1]}
        castShadow
      >
        <meshStandardMaterial 
          color="#FFD700" 
          roughness={0.2}
          metalness={0.8}
        />
      </Box>

      <Box 
        args={[0.1, 0.3, 0.1]} 
        position={[0.8, 3, 2.1]}
        castShadow
      >
        <meshStandardMaterial 
          color="#FFD700" 
          roughness={0.2}
          metalness={0.8}
        />
      </Box>

      {/* Interaction Indicator */}
      {isNearStore && (
        <Text
          position={[0, 9, 2.5]}
          fontSize={0.5}
          color="#00ff00"
          anchorX="center"
          anchorY="middle"
          font="/fonts/Inter-Bold.woff"
        >
          اضغط للدخول
        </Text>
      )}
    </group>
  )
}
