import { useRef } from 'react'
import { Mesh } from 'three'
import { useFrame } from '@react-three/fiber'
import { Box, Plane, Text } from '@react-three/drei'

export default function MallEnvironment() {
  const floorRef = useRef<Mesh>(null)

  return (
    <group>
      {/* Floor */}
      <Plane 
        ref={floorRef}
        args={[100, 100]} 
        rotation={[-Math.PI / 2, 0, 0]} 
        position={[0, 0, 0]}
        receiveShadow
      >
        <meshStandardMaterial 
          color="#2a2a2a" 
          roughness={0.1}
          metalness={0.1}
        />
      </Plane>

      {/* Mall Ceiling */}
      <Plane 
        args={[100, 100]} 
        rotation={[Math.PI / 2, 0, 0]} 
        position={[0, 15, 0]}
      >
        <meshStandardMaterial 
          color="#1a1a1a" 
          roughness={0.8}
        />
      </Plane>

      {/* Mall Walls */}
      {/* Back Wall */}
      <Plane 
        args={[100, 15]} 
        position={[0, 7.5, -50]}
      >
        <meshStandardMaterial 
          color="#333333" 
          roughness={0.7}
        />
      </Plane>

      {/* Left Wall */}
      <Plane 
        args={[100, 15]} 
        rotation={[0, Math.PI / 2, 0]}
        position={[-50, 7.5, 0]}
      >
        <meshStandardMaterial 
          color="#333333" 
          roughness={0.7}
        />
      </Plane>

      {/* Right Wall */}
      <Plane 
        args={[100, 15]} 
        rotation={[0, -Math.PI / 2, 0]}
        position={[50, 7.5, 0]}
      >
        <meshStandardMaterial 
          color="#333333" 
          roughness={0.7}
        />
      </Plane>

      {/* Mall Pillars */}
      {Array.from({ length: 8 }, (_, i) => {
        const x = (i % 4) * 20 - 30
        const z = Math.floor(i / 4) * 30 - 15
        return (
          <Box 
            key={i}
            args={[2, 15, 2]} 
            position={[x, 7.5, z]}
            castShadow
          >
            <meshStandardMaterial 
              color="#444444" 
              roughness={0.5}
              metalness={0.2}
            />
          </Box>
        )
      })}

      {/* Mall Sign */}
      <Text
        position={[0, 12, -49]}
        fontSize={3}
        color="#00ffff"
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.woff"
      >
        V MALL
      </Text>

      {/* Decorative Elements */}
      {/* Central Fountain Area */}
      <Box 
        args={[8, 1, 8]} 
        position={[0, 0.5, 0]}
        receiveShadow
      >
        <meshStandardMaterial 
          color="#1a4a6b" 
          roughness={0.1}
          metalness={0.8}
        />
      </Box>

      {/* Fountain */}
      <Box 
        args={[1, 3, 1]} 
        position={[0, 1.5, 0]}
        castShadow
      >
        <meshStandardMaterial 
          color="#666666" 
          roughness={0.3}
          metalness={0.7}
        />
      </Box>
    </group>
  )
}
