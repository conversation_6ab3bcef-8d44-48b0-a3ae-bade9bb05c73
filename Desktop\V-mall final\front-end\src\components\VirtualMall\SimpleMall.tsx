import { Canvas } from '@react-three/fiber'
import { OrbitControls, Box, Plane, Text } from '@react-three/drei'
import { Suspense } from 'react'

export default function SimpleMall() {
  return (
    <div className="w-full h-screen bg-gray-900 relative">
      {/* UI Controls */}
      <div className="absolute top-4 left-4 z-10 bg-black/80 text-white p-4 rounded-lg">
        <h3 className="text-lg font-bold mb-2">التحكم</h3>
        <div className="space-y-1 text-sm">
          <div>التحرك: استخدم الماوس للنظر حولك</div>
          <div>التكبير: عجلة الماوس</div>
        </div>
      </div>

      {/* Exit Button */}
      <div className="absolute top-4 right-4 z-10">
        <button 
          onClick={() => window.history.back()}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          خروج
        </button>
      </div>

      {/* 3D Canvas */}
      <Canvas shadows camera={{ position: [0, 8, 15], fov: 75 }}>
        <Suspense fallback={null}>
          {/* Very bright lighting */}
          <ambientLight intensity={2} />
          <directionalLight 
            position={[10, 20, 10]} 
            intensity={3} 
            castShadow 
          />
          <pointLight position={[0, 15, 0]} intensity={2} />
          <pointLight position={[-20, 10, -20]} intensity={1.5} />
          <pointLight position={[20, 10, -20]} intensity={1.5} />
          <pointLight position={[-20, 10, 20]} intensity={1.5} />
          <pointLight position={[20, 10, 20]} intensity={1.5} />
          
          {/* Simple Mall Environment */}
          <group>
            {/* Floor */}
            <Plane 
              args={[100, 100]} 
              rotation={[-Math.PI / 2, 0, 0]} 
              position={[0, 0, 0]}
              receiveShadow
            >
              <meshStandardMaterial color="#333333" roughness={0.1} metalness={0.8} />
            </Plane>

            {/* Walls */}
            <Plane args={[100, 15]} position={[0, 7.5, -50]}>
              <meshStandardMaterial color="#f0f0f0" />
            </Plane>
            <Plane args={[100, 15]} rotation={[0, Math.PI / 2, 0]} position={[-50, 7.5, 0]}>
              <meshStandardMaterial color="#f0f0f0" />
            </Plane>
            <Plane args={[100, 15]} rotation={[0, -Math.PI / 2, 0]} position={[50, 7.5, 0]}>
              <meshStandardMaterial color="#f0f0f0" />
            </Plane>

            {/* Ceiling */}
            <Plane args={[100, 100]} rotation={[Math.PI / 2, 0, 0]} position={[0, 15, 0]}>
              <meshStandardMaterial color="#ffffff" />
            </Plane>

            {/* Simple Stores */}
            {[
              { name: "Nike", position: [-20, 5, -30], color: "#FF6B35", website: "https://www.nike.com" },
              { name: "Apple", position: [0, 5, -30], color: "#007AFF", website: "https://www.apple.com" },
              { name: "Adidas", position: [20, 5, -30], color: "#000000", website: "https://www.adidas.com" },
              { name: "Samsung", position: [-30, 5, 0], color: "#1428A0", website: "https://www.samsung.com" },
              { name: "Zara", position: [30, 5, 0], color: "#000000", website: "https://www.zara.com" },
              { name: "H&M", position: [-20, 5, 30], color: "#E50000", website: "https://www2.hm.com" },
              { name: "Starbucks", position: [0, 5, 30], color: "#00704A", website: "https://www.starbucks.com" },
              { name: "McDonald's", position: [20, 5, 30], color: "#FFC72C", website: "https://www.mcdonalds.com" }
            ].map((store, index) => (
              <group key={index} position={store.position}>
                {/* Store Building */}
                <Box 
                  args={[8, 10, 4]} 
                  castShadow 
                  receiveShadow
                  onClick={() => window.open(store.website, '_blank')}
                  onPointerEnter={(e) => {
                    e.object.material.color.setHex(0xffffff)
                    document.body.style.cursor = 'pointer'
                  }}
                  onPointerLeave={(e) => {
                    e.object.material.color.setHex(0xf8f8f8)
                    document.body.style.cursor = 'default'
                  }}
                >
                  <meshStandardMaterial color="#f8f8f8" />
                </Box>
                
                {/* Store Sign */}
                <Box args={[6, 1.5, 0.2]} position={[0, 6, 2.2]} castShadow>
                  <meshStandardMaterial 
                    color={store.color} 
                    emissive={store.color}
                    emissiveIntensity={0.3}
                  />
                </Box>
                
                {/* Store Name */}
                <Text
                  position={[0, 6, 2.3]}
                  fontSize={0.8}
                  color="white"
                  anchorX="center"
                  anchorY="middle"
                >
                  {store.name}
                </Text>

                {/* Glass Front */}
                <Plane args={[7, 8]} position={[0, 5, 2.1]}>
                  <meshStandardMaterial 
                    color="#87CEEB" 
                    transparent 
                    opacity={0.3}
                    roughness={0.1}
                    metalness={0.9}
                  />
                </Plane>

                {/* Store Lighting */}
                <pointLight 
                  position={[0, 8, 2]} 
                  intensity={1} 
                  color={store.color}
                  distance={10}
                />
              </group>
            ))}

            {/* Central Feature */}
            <Box args={[6, 2, 6]} position={[0, 1, 0]} castShadow>
              <meshStandardMaterial 
                color="#4a90e2" 
                roughness={0.2}
                metalness={0.8}
              />
            </Box>

            {/* Mall Sign */}
            <Text
              position={[0, 12, -49]}
              fontSize={4}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              V MALL
            </Text>

            {/* Pillars */}
            {Array.from({ length: 8 }, (_, i) => {
              const x = (i % 4) * 25 - 37.5
              const z = Math.floor(i / 4) * 40 - 20
              return (
                <Box 
                  key={i}
                  args={[2, 15, 2]} 
                  position={[x, 7.5, z]}
                  castShadow
                >
                  <meshStandardMaterial 
                    color="#f5f5f5" 
                    roughness={0.2}
                    metalness={0.3}
                  />
                </Box>
              )
            })}
          </group>
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minDistance={5}
            maxDistance={50}
          />
        </Suspense>
      </Canvas>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 z-10 bg-black/80 text-white p-4 rounded-lg max-w-sm">
        <h4 className="font-bold mb-2">كيفية الاستخدام:</h4>
        <ul className="text-sm space-y-1">
          <li>• اضغط واسحب بالماوس للنظر حولك</li>
          <li>• استخدم عجلة الماوس للتكبير والتصغير</li>
          <li>• اضغط على المحلات لزيارة مواقعها</li>
        </ul>
      </div>
    </div>
  )
}
