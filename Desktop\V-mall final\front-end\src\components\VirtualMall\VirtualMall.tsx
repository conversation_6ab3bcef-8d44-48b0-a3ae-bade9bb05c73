import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei'
import { Suspense } from 'react'
import MallEnvironment from './MallEnvironment'
import Avatar from './Avatar'
import StoreManager from './StoreManager'
import UI from './UI'

export default function VirtualMall() {
  return (
    <div className="w-full h-screen bg-black relative">
      {/* 3D Canvas */}
      <Canvas shadows>
        <Suspense fallback={null}>
          {/* Camera */}
          <PerspectiveCamera makeDefault position={[0, 8, 15]} fov={75} />
          
          {/* Lighting - Much brighter like the image */}
          <ambientLight intensity={1.2} color="#ffffff" />
          <directionalLight
            position={[10, 20, 10]}
            intensity={2}
            color="#ffffff"
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={100}
            shadow-camera-left={-50}
            shadow-camera-right={50}
            shadow-camera-top={50}
            shadow-camera-bottom={-50}
          />

          {/* Mall ceiling lights */}
          <pointLight position={[0, 14, 0]} intensity={2} color="#ffffff" />
          <pointLight position={[-15, 14, -15]} intensity={1.5} color="#ffffff" />
          <pointLight position={[15, 14, -15]} intensity={1.5} color="#ffffff" />
          <pointLight position={[-15, 14, 15]} intensity={1.5} color="#ffffff" />
          <pointLight position={[15, 14, 15]} intensity={1.5} color="#ffffff" />

          {/* Blue accent lighting like in the image */}
          <pointLight position={[0, 8, -40]} intensity={1} color="#00bfff" />
          <pointLight position={[-30, 8, 0]} intensity={1} color="#00bfff" />
          <pointLight position={[30, 8, 0]} intensity={1} color="#00bfff" />
          
          {/* Environment */}
          <Environment preset="city" />
          
          {/* Mall Components */}
          <MallEnvironment />
          <StoreManager />
          <Avatar />
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minDistance={5}
            maxDistance={50}
          />
        </Suspense>
      </Canvas>
      
      {/* UI Overlay */}
      <UI />
    </div>
  )
}
