import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei'
import { Suspense } from 'react'
import MallEnvironment from './MallEnvironment'
import Avatar from './Avatar'
import StoreManager from './StoreManager'
import UI from './UI'

export default function VirtualMall() {
  return (
    <div className="w-full h-screen bg-black relative">
      {/* 3D Canvas */}
      <Canvas shadows>
        <Suspense fallback={null}>
          {/* Camera */}
          <PerspectiveCamera makeDefault position={[0, 5, 10]} />
          
          {/* Lighting */}
          <ambientLight intensity={0.3} />
          <directionalLight 
            position={[10, 10, 5]} 
            intensity={1} 
            castShadow 
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[0, 10, 0]} intensity={0.5} />
          
          {/* Environment */}
          <Environment preset="city" />
          
          {/* Mall Components */}
          <MallEnvironment />
          <StoreManager />
          <Avatar />
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minDistance={5}
            maxDistance={50}
          />
        </Suspense>
      </Canvas>
      
      {/* UI Overlay */}
      <UI />
    </div>
  )
}
