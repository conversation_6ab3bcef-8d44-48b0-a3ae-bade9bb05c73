import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei'
import { Suspense } from 'react'
import MallEnvironment from './MallEnvironment'
import Avatar from './Avatar'
import StoreManager from './StoreManager'
import UI from './UI'

export default function VirtualMall() {
  return (
    <div className="w-full h-screen bg-black relative">
      {/* 3D Canvas */}
      <Canvas shadows>
        <Suspense fallback={null}>
          {/* Camera */}
          <PerspectiveCamera makeDefault position={[0, 5, 10]} />
          
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 15, 5]}
            intensity={1.2}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={100}
            shadow-camera-left={-50}
            shadow-camera-right={50}
            shadow-camera-top={50}
            shadow-camera-bottom={-50}
          />
          <pointLight position={[0, 12, 0]} intensity={0.8} color="#ffffff" />
          <pointLight position={[-20, 8, -20]} intensity={0.6} color="#4a90e2" />
          <pointLight position={[20, 8, -20]} intensity={0.6} color="#4a90e2" />
          <pointLight position={[-20, 8, 20]} intensity={0.6} color="#4a90e2" />
          <pointLight position={[20, 8, 20]} intensity={0.6} color="#4a90e2" />
          <spotLight
            position={[0, 20, 0]}
            angle={0.3}
            penumbra={1}
            intensity={1}
            castShadow
            target-position={[0, 0, 0]}
          />
          
          {/* Environment */}
          <Environment preset="city" />
          
          {/* Mall Components */}
          <MallEnvironment />
          <StoreManager />
          <Avatar />
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minDistance={5}
            maxDistance={50}
          />
        </Suspense>
      </Canvas>
      
      {/* UI Overlay */}
      <UI />
    </div>
  )
}
