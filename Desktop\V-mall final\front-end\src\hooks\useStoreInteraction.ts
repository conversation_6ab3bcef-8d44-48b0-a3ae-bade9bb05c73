import { useState, useEffect } from 'react'
import { Vector3 } from 'three'
import { useFrame } from '@react-three/fiber'

// This hook will be used to detect when the avatar is near a store
export function useStoreInteraction(storeId: number, storePosition: Vector3) {
  const [isNearStore, setIsNearStore] = useState(false)
  const [showShopButton, setShowShopButton] = useState(false)
  const [avatarPosition, setAvatarPosition] = useState(new Vector3(0, 2, 5))

  // Distance threshold for interaction
  const INTERACTION_DISTANCE = 8

  useFrame(() => {
    // Calculate distance between avatar and store
    const distance = avatarPosition.distanceTo(storePosition)
    
    const wasNear = isNearStore
    const isCurrentlyNear = distance < INTERACTION_DISTANCE
    
    setIsNearStore(isCurrentlyNear)
    
    // Show shop button with a slight delay for better UX
    if (isCurrentlyNear && !wasNear) {
      setTimeout(() => setShowShopButton(true), 200)
    } else if (!isCurrentlyNear && wasNear) {
      setShowShopButton(false)
    }
  })

  // Function to update avatar position (will be called from Avatar component)
  const updateAvatarPosition = (newPosition: Vector3) => {
    setAvatarPosition(newPosition)
  }

  return {
    isNearStore,
    showShopButton,
    updateAvatarPosition
  }
}
